import asyncio
import logging
import os
import time
from functools import wraps
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, field
from pathlib import Path
from datetime import datetime

from dotenv import load_dotenv
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_core.messages import HumanMessage
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import InMemorySaver

# Rich imports
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich.progress import Progress, TaskID, BarColumn, TextColumn, TimeRemainingColumn
from rich.panel import Panel
from rich.columns import Columns
from rich.tree import Tree
from rich.text import Text
from rich.live import Live
from rich.layout import Layout
from rich.align import Align
from rich.markdown import Markdown
from rich.syntax import Syntax
from rich.status import Status
from rich import box
from rich.rule import Rule


# ===== НАСТРОЙКА ЛОГИРОВАНИЯ С ПОДАВЛЕНИЕМ ПРЕДУПРЕЖДЕНИЙ =====
class IgnoreSchemaWarnings(logging.Filter):
    def filter(self, record):
        ignore_messages = [
            "Key 'additionalProperties' is not supported in schema, ignoring",
            "Key '$schema' is not supported in schema, ignoring"
        ]
        return not any(msg in record.getMessage() for msg in ignore_messages)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ai_agent.log', encoding='utf-8')
    ]
)

# Применить фильтр ко всем обработчикам
for handler in logging.root.handlers:
    handler.addFilter(IgnoreSchemaWarnings())

# Дополнительно подавить логгеры MCP компонентов
mcp_loggers = [
    'langchain_mcp_adapters',
    'mcp',
    'jsonschema'
]
for logger_name in mcp_loggers:
    logging.getLogger(logger_name).setLevel(logging.ERROR)

logger = logging.getLogger(__name__)


# ===== ДЕКОРАТОРЫ =====
def retry_on_failure(max_retries: int = 2, delay: float = 1.0):
    """Декоратор для повторения операций при неудаче"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        logger.warning(f"Попытка {attempt + 1} неудачна, повтор через {delay}с")
                        await asyncio.sleep(delay)
            raise last_exception
        return wrapper
    return decorator


# ===== УПРОЩЕННАЯ КОНФИГУРАЦИЯ =====
@dataclass
class AgentConfig:
    """Упрощенная конфигурация AI-агента для Gemini"""
    filesystem_path: str = None  # По умолчанию None
    use_memory: bool = True
    model_name: str = "gemini-2.0-flash"
    temperature: float = 0.0
    
    def __post_init__(self):
        """Автоматическая установка рабочей директории при инициализации"""
        if self.filesystem_path is None:
            self.filesystem_path = os.getcwd()
            logger.info(f"Рабочая директория не указана, используется текущая: {self.filesystem_path}")
        
        # Нормализация пути (добавление завершающего слеша)
        if not self.filesystem_path.endswith(os.sep):
            self.filesystem_path += os.sep
    
    def validate(self) -> None:
        """Простая валидация"""
        if not os.path.exists(self.filesystem_path):
            raise ValueError(f"Путь не существует: {self.filesystem_path}")
        
        if not os.getenv("GOOGLE_API_KEY"):
            raise ValueError("Отсутствует переменная окружения: GOOGLE_API_KEY")
    
    def get_mcp_config(self) -> Dict[str, Any]:
        """Конфигурация MCP сервера"""
        return {
            "filesystem": {
                "command": "npx",
                "args": ["-y", "@modelcontextprotocol/server-filesystem", self.filesystem_path],
                "transport": "stdio"
            }
        }


# ===== ОСНОВНОЙ КЛАСС АГЕНТА =====
class FileSystemAgent:
    """
    Упрощенный AI-агент для работы с файловой системой (только Gemini)
    """
    
    def __init__(self, config: AgentConfig):
        self.config = config
        self.agent = None
        self.checkpointer = None
        self.mcp_client = None
        self.tools = []
        self._initialized = False
        
        logger.info("Создан агент с Gemini")
        logger.info(f"Рабочая директория: {config.filesystem_path}")
    
    @property
    def is_ready(self) -> bool:
        """Проверяет готовность агента"""
        return self._initialized and self.agent is not None
    
    async def initialize(self) -> bool:
        """Инициализация агента"""
        if self._initialized:
            logger.warning("Агент уже инициализирован")
            return True
        
        logger.info("Инициализация агента...")
        
        try:
            self.config.validate()
            await self._init_mcp_client()
            
            # Создание Gemini модели
            api_key = os.getenv("GOOGLE_API_KEY")
            model = ChatGoogleGenerativeAI(
                model=self.config.model_name,
                google_api_key=api_key,
                temperature=self.config.temperature
            )
            
            if self.config.use_memory:
                self.checkpointer = InMemorySaver()
                logger.info("Память агента включена")
            
            self.agent = create_react_agent(
                model=model,
                tools=self.tools,
                checkpointer=self.checkpointer,
                prompt=self._get_system_prompt()
            )
            
            self._initialized = True
            logger.info("✅ Агент успешно инициализирован")
            return True
            
        except Exception as e:
            logger.error(f"❌ Ошибка инициализации: {e}")
            return False
    
    @retry_on_failure()
    async def _init_mcp_client(self):
        """Инициализация MCP клиента"""
        logger.info("Инициализация MCP клиента...")
        
        # Временно подавить предупреждения во время инициализации
        old_level = logging.getLogger().level
        logging.getLogger().setLevel(logging.ERROR)
        
        try:
            self.mcp_client = MultiServerMCPClient(self.config.get_mcp_config())
            self.tools = await self.mcp_client.get_tools()
        finally:
            # Восстановить уровень логирования
            logging.getLogger().setLevel(old_level)
        
        if not self.tools:
            raise Exception("Нет доступных MCP инструментов")
        
        logger.info(f"Загружено {len(self.tools)} инструментов")
        for tool in self.tools:
            logger.info(f"  • {tool.name}")
    
    def _get_system_prompt(self) -> str:
        """Системный промпт"""
        # Получаем список доступных инструментов для более точного промпта
        available_tools = [tool.name for tool in self.tools] if self.tools else []
        tools_info = ", ".join(available_tools) if available_tools else "нет доступных инструментов"
        
        return (
            "Ты полезный AI-ассистент для работы с файлами и папками. "
            f"Доступные инструменты: {tools_info}\n\n"
            "Основные операции:\n"
            "- Создание файлов: используй инструмент с 'write' в названии\n"
            "- Создание папок: используй инструмент с 'directory' в названии\n"
            "- Чтение файлов: используй инструмент с 'read' в названии\n"
            "- Удаление файлов: используй инструмент с 'delete' или 'remove' в названии\n"
            "- Перемещение файлов: используй инструмент с 'move' в названии\n"
            "- Список файлов: используй инструмент с 'list' в названии\n\n"
            f"ВАЖНО: Все пути должны быть внутри рабочей директории: '{self.config.filesystem_path}'\n"
            "Используй ТОЛЬКО предоставленные инструменты.\n\n"
            "Примеры команд:\n"
            "- 'создай файл test.txt с текстом Hello' → найди инструмент для записи файла\n"
            "- 'удали файл old.txt' → найди инструмент для удаления файла\n"
            "- 'покажи содержимое папки' → найди инструмент для списка файлов\n"
            "- 'прочитай файл config.py' → найди инструмент для чтения файла\n\n"
            "При успехе отвечай кратко и подтверждай выполненное действие.\n"
            "Если нужного инструмента нет - четко объясни, какой инструмент отсутствует."
        )

    
    @retry_on_failure()
    async def process_message(self, user_input: str, thread_id: str = "default") -> str:
        """Обработка сообщения пользователя"""
        if not self.is_ready:
            return "❌ Агент не готов. Попробуйте переинициализировать."
        
        try:
            # Явное указание рабочей директории в каждом запросе
            context_input = (
                f"Рабочая директория: '{self.config.filesystem_path}'. "
                f"Все файловые операции должны выполняться внутри этой директории. "
                f"Запрос: {user_input}"
            )
            
            config = {"configurable": {"thread_id": thread_id}}
            message_input = {"messages": [HumanMessage(content=context_input)]}
            
            response = await self.agent.ainvoke(message_input, config)
            
            # ИСПРАВЛЕНИЕ: правильно извлекаем последнее сообщение
            if isinstance(response, dict) and "messages" in response:
                messages = response["messages"]
                if messages:
                    last_message = messages[-1]
                    # Извлекаем содержимое в зависимости от типа сообщения
                    if hasattr(last_message, 'content'):
                        return str(last_message.content)
                    elif isinstance(last_message, dict) and 'content' in last_message:
                        return str(last_message['content'])
                    else:
                        return str(last_message)
                else:
                    return "❌ Получен пустой ответ от агента"
            else:
                # Если response не в ожидаемом формате
                return str(response)
            
        except Exception as e:
            error_msg = f"❌ Ошибка обработки: {e}"
            logger.error(error_msg)
            logger.error(f"Тип ответа: {type(response)}")
            if 'response' in locals():
                logger.error(f"Содержимое ответа: {response}")
            return error_msg
    
    def get_status(self) -> Dict[str, Any]:
        """Информация о состоянии агента"""
        return {
            "initialized": self._initialized,
            "model": "Gemini",
            "model_name": self.config.model_name,
            "filesystem_path": self.config.filesystem_path,
            "memory_enabled": self.config.use_memory,
            "tools_count": len(self.tools)
        }


# ===== БОГАТЫЙ ТЕРМИНАЛЬНЫЙ ИНТЕРФЕЙС =====
class RichInteractiveChat:
    """Богатый терминальный интерфейс для AI-агента"""
    
    def __init__(self, agent):
        self.console = Console()
        self.agent = agent
        self.history = []
        self.current_thread = "main"
        self.show_timestamps = True
        self.theme = "dark"
        
        # Стили
        self.styles = {
            "user": "bold blue",
            "agent": "green",
            "system": "yellow",
            "error": "bold red",
            "success": "bold green",
            "info": "cyan",
            "warning": "orange3",
            "path": "bold magenta",
            "command": "bold white on blue"
        }
    
    def clear_screen(self):
        """Очистка экрана"""
        self.console.clear()
    
    def print_header(self):
        """Отображение заголовка приложения"""
        header_text = Text("🤖 Gemini FileSystem Agent", style="bold white")
        header_panel = Panel(
            Align.center(header_text),
            box=box.DOUBLE,
            border_style="blue",
            padding=(1, 2)
        )
        self.console.print(header_panel)
        self.console.print()
    
    def print_status_bar(self):
        """Статус-бар с информацией о системе"""
        if not self.agent:
            return
            
        status = self.agent.get_status()
        
        status_items = [
            f"🔧 [bold]{status.get('model', 'Unknown')}[/bold]",
            f"📁 [bold magenta]{os.path.basename(status.get('filesystem_path', ''))}/[/bold magenta]",
            f"🧠 [{'green' if status.get('memory_enabled') else 'red'}]Memory[/]",
            f"🔧 {status.get('tools_count', 0)} tools",
            f"💬 Thread: [bold]{self.current_thread}[/bold]"
        ]
        
        status_table = Table.grid(padding=1)
        for item in status_items:
            status_table.add_column()
        
        status_table.add_row(*status_items)
        
        status_panel = Panel(
            status_table,
            title="[bold]System Status[/bold]",
            border_style="dim",
            height=3
        )
        
        self.console.print(status_panel)
    
    def display_file_tree(self, path: str, max_depth: int = 3, show_hidden: bool = False):
        """Красивое отображение файловой структуры"""
        def add_tree_items(tree_node, current_path, current_depth: int = 0):
            if current_depth >= max_depth:
                return
            
            try:
                current_path = Path(current_path)
                items = sorted(current_path.iterdir(), key=lambda x: (x.is_file(), x.name.lower()))
                
                for item in items:
                    if not show_hidden and item.name.startswith('.'):
                        continue
                    
                    if item.is_dir():
                        # Директория
                        emoji = "📁"
                        try:
                            if any(item.iterdir()):
                                emoji = "📁"
                            else:
                                emoji = "📂"
                        except:
                            emoji = "📁"
                        
                        dir_node = tree_node.add(f"{emoji} [bold blue]{item.name}/[/bold blue]")
                        if current_depth < max_depth - 1:
                            add_tree_items(dir_node, item, current_depth + 1)
                    else:
                        # Файл
                        try:
                            size = item.stat().st_size
                            size_str = self._format_file_size(size)
                            
                            # Эмодзи и цвет по расширению
                            emoji = self._get_file_emoji(item.suffix.lower())
                            color = self._get_file_color(item.suffix.lower())
                            
                            tree_node.add(f"{emoji} [{color}]{item.name}[/{color}] [dim]({size_str})[/dim]")
                        except:
                            tree_node.add(f"📄 {item.name}")
                        
            except PermissionError:
                tree_node.add("❌ [red]Permission Denied[/red]")
            except Exception as e:
                tree_node.add(f"❌ [red]Error: {str(e)}[/red]")
        
        tree = Tree(f"📁 [bold blue]{Path(path).name}/[/bold blue]")
        add_tree_items(tree, path)
        
        panel = Panel(
            tree,
            title=f"[bold]File Tree: {path}[/bold]",
            border_style="blue",
            expand=False
        )
        
        self.console.print(panel)
    
    def _format_file_size(self, size_bytes: int) -> str:
        """Форматирование размера файла"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    def _get_file_emoji(self, extension: str) -> str:
        """Получение эмодзи для файла по расширению"""
        emoji_map = {
            '.py': '🐍', '.js': '🟨', '.html': '🌐', '.css': '🎨',
            '.json': '📋', '.xml': '📄', '.yml': '⚙️', '.yaml': '⚙️',
            '.md': '📝', '.txt': '📄', '.pdf': '📕', '.doc': '📘',
            '.xlsx': '📊', '.csv': '📈', '.zip': '🗜️', '.tar': '📦',
            '.jpg': '🖼️', '.png': '🖼️', '.gif': '🎞️', '.mp4': '🎬',
            '.mp3': '🎵', '.wav': '🎵', '.exe': '⚙️', '.dll': '🔧',
            '.log': '📋', '.sql': '🗃️', '.db': '🗃️', '.sqlite': '🗃️'
        }
        return emoji_map.get(extension, '📄')
    
    def _get_file_color(self, extension: str) -> str:
        """Получение цвета для файла по расширению"""
        color_map = {
            '.py': 'yellow', '.js': 'bright_yellow', '.html': 'bright_blue',
            '.css': 'magenta', '.json': 'cyan', '.xml': 'green',
            '.md': 'bright_white', '.txt': 'white', '.log': 'dim white',
            '.jpg': 'bright_magenta', '.png': 'bright_magenta',
            '.zip': 'red', '.exe': 'bright_red'
        }
        return color_map.get(extension, 'white')
    
    def display_help(self):
        """Отображение справки по командам"""
        commands = {
            "Файловые операции": [
                ("создай файл <имя>", "Создать новый файл"),
                ("создай папку <имя>", "Создать новую папку"),
                ("покажи файлы", "Показать структуру файлов"),
                ("читай <файл>", "Прочитать содержимое файла"),
                ("удали <файл>", "Удалить файл или папку"),
            ],
            "Системные команды": [
                ("/help", "Показать эту справку"),
                ("/clear", "Очистить экран"),
                ("/status", "Показать статус системы"),
                ("/tools", "Показать доступные инструменты MCP"),
                ("/tree [путь]", "Показать дерево файлов"),
                ("/history", "Показать историю команд"),
                ("/thread <имя>", "Переключить контекст"),
                ("/quit", "Выйти из программы"),
            ],
            "Настройки": [
                ("/theme <light|dark>", "Сменить тему"),
                ("/timestamps <on|off>", "Включить/выключить временные метки"),
                ("/export", "Экспорт истории диалога"),
            ]
        }
        
        help_panels = []
        for category, cmd_list in commands.items():
            table = Table(show_header=False, box=None, pad_edge=False)
            table.add_column("Command", style="bold cyan", no_wrap=True)
            table.add_column("Description", style="dim")
            
            for cmd, desc in cmd_list:
                table.add_row(cmd, desc)
            
            panel = Panel(
                table,
                title=f"[bold]{category}[/bold]",
                border_style="blue"
            )
            help_panels.append(panel)
        
        self.console.print(Columns(help_panels, equal=True, expand=True))
    
    def display_history(self, limit: int = 10):
        """Отображение истории команд"""
        if not self.history:
            self.console.print("[dim]История пуста[/dim]")
            return
        
        table = Table(
            "Time", "Type", "Message",
            title="[bold]Command History[/bold]",
            show_lines=True
        )
        
        recent_history = self.history[-limit:] if len(self.history) > limit else self.history
        
        for entry in recent_history:
            timestamp = entry.get('timestamp', '')
            msg_type = entry.get('type', 'unknown')
            message = entry.get('message', '')[:100] + ('...' if len(entry.get('message', '')) > 100 else '')
            
            style = self.styles.get(msg_type, "white")
            table.add_row(
                timestamp,
                f"[{style}]{msg_type.upper()}[/{style}]",
                message
            )
        
        self.console.print(table)
    
    def display_tools_info(self):
        """Отображение доступных MCP инструментов"""
        if not self.agent or not self.agent.tools:
            self.console.print("[red]❌ Нет доступных инструментов[/red]")
            return
        
        table = Table(
            title="[bold]Available MCP Tools[/bold]", 
            box=box.ROUNDED,
            show_header=True
        )
        table.add_column("Tool Name", style="cyan", no_wrap=True)
        table.add_column("Description", style="white")
        
        for tool in self.agent.tools:
            # Получаем описание инструмента
            description = ""
            if hasattr(tool, 'description') and tool.description:
                description = tool.description[:80] + ('...' if len(tool.description) > 80 else '')
            elif hasattr(tool, 'args_schema') and tool.args_schema:
                # Попробуем извлечь информацию из схемы
                try:
                    if hasattr(tool.args_schema, '__doc__') and tool.args_schema.__doc__:
                        description = tool.args_schema.__doc__[:80]
                except:
                    pass
            
            if not description:
                description = "Нет описания"
            
            table.add_row(tool.name, description)
        
        self.console.print(table)
        
        # Дополнительная информация
        self.console.print(f"\n[dim]Всего инструментов: {len(self.agent.tools)}[/dim]")
        self.console.print("[dim]Для использования просто описывайте желаемое действие естественным языком[/dim]")
    
    def display_agent_response(self, response: str, response_time: float = None):
        """Красивое отображение ответа агента"""
        # Определяем тип контента
        if response.startswith('```') and response.endswith('```'):
            # Код
            lines = response.strip('`').split('\n')
            language = lines[0] if lines[0] else 'text'
            code = '\n'.join(lines[1:])
            
            syntax = Syntax(code, language, theme="monokai", line_numbers=True)
            panel = Panel(
                syntax,
                title="[bold green]🤖 Gemini Response (Code)[/bold green]",
                border_style="green"
            )
        else:
            # Обычный текст или markdown
            try:
                # Попробуем интерпретировать как markdown
                content = Markdown(response)
            except:
                content = Text(response)
            
            panel = Panel(
                content,
                title="[bold green]🤖 Gemini Response[/bold green]",
                border_style="green"
            )
        
        self.console.print(panel)
        
        # Показать время ответа, если доступно
        if response_time:
            self.console.print(f"[dim]⏱️ Response time: {response_time:.2f}s[/dim]")
    
    def display_error(self, error_message: str):
        """Отображение ошибки"""
        panel = Panel(
            f"❌ {error_message}",
            title="[bold red]Error[/bold red]",
            border_style="red"
        )
        self.console.print(panel)
    
    def display_success(self, message: str):
        """Отображение успешного выполнения"""
        panel = Panel(
            f"✅ {message}",
            title="[bold green]Success[/bold green]",
            border_style="green"
        )
        self.console.print(panel)
    
    def get_user_input(self) -> Optional[str]:
        """Получение ввода пользователя с красивым промптом"""
        try:
            prompt_text = Text()
            prompt_text.append("💬 You", style="bold blue")
            prompt_text.append(" › ", style="dim")
            
            user_input = Prompt.ask(prompt_text, console=self.console).strip()
            
            if user_input.lower() in ['quit', 'exit', '/quit']:
                return None
            
            return user_input
            
        except (KeyboardInterrupt, EOFError):
            self.console.print("\n[dim]Goodbye! 👋[/dim]")
            return None
    
    def process_system_command(self, command: str) -> bool:
        """Обработка системных команд"""
        parts = command.split()
        cmd = parts[0].lower()
        
        if cmd == '/help':
            self.display_help()
            return True
        
        elif cmd == '/clear':
            self.clear_screen()
            self.print_header()
            self.print_status_bar()
            return True
        
        elif cmd == '/status' and self.agent:
            status = self.agent.get_status()
            self.display_status_info(status)
            return True
        
        elif cmd == '/tools' and self.agent:
            self.display_tools_info()
            return True
        
        elif cmd == '/tree':
            path = parts[1] if len(parts) > 1 else (
                self.agent.config.filesystem_path if self.agent else os.getcwd()
            )
            self.display_file_tree(path)
            return True
        
        elif cmd == '/history':
            limit = int(parts[1]) if len(parts) > 1 and parts[1].isdigit() else 10
            self.display_history(limit)
            return True
        
        elif cmd == '/thread':
            if len(parts) > 1:
                self.current_thread = parts[1]
                self.console.print(f"[green]✅ Switched to thread: {self.current_thread}[/green]")
            else:
                self.console.print(f"[blue]Current thread: {self.current_thread}[/blue]")
            return True
        
        elif cmd == '/theme':
            if len(parts) > 1 and parts[1] in ['light', 'dark']:
                self.theme = parts[1]
                self.console.print(f"[green]✅ Theme changed to: {self.theme}[/green]")
            else:
                self.console.print(f"[blue]Current theme: {self.theme}[/blue]")
            return True
        
        elif cmd == '/timestamps':
            if len(parts) > 1 and parts[1] in ['on', 'off']:
                self.show_timestamps = parts[1] == 'on'
                status = "enabled" if self.show_timestamps else "disabled" 
                self.console.print(f"[green]✅ Timestamps {status}[/green]")
            else:
                status = "enabled" if self.show_timestamps else "disabled"
                self.console.print(f"[blue]Timestamps: {status}[/blue]")
            return True
        
        elif cmd == '/export':
            self.export_history()
            return True
        
        return False
    
    def display_status_info(self, status: Dict[str, Any]):
        """Подробное отображение статуса системы"""
        table = Table(title="[bold]System Status Details[/bold]", box=box.ROUNDED)
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="green")
        
        for key, value in status.items():
            table.add_row(key.replace('_', ' ').title(), str(value))
        
        self.console.print(table)
    
    def export_history(self):
        """Экспорт истории в файл"""
        if not self.history:
            self.console.print("[yellow]⚠️ История пуста[/yellow]")
            return
        
        filename = f"chat_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("# Gemini AI Agent Chat History\n\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                for entry in self.history:
                    f.write(f"## {entry['type'].upper()} - {entry['timestamp']}\n\n")
                    f.write(f"{entry['message']}\n\n---\n\n")
            
            self.console.print(f"[green]✅ История экспортирована в: {filename}[/green]")
        except Exception as e:
            self.console.print(f"[red]❌ Ошибка экспорта: {e}[/red]")
    
    def add_to_history(self, message: str, msg_type: str = "user"):
        """Добавление сообщения в историю"""
        timestamp = datetime.now().strftime('%H:%M:%S') if self.show_timestamps else ''
        
        self.history.append({
            'timestamp': timestamp,
            'type': msg_type,
            'message': message,
            'thread': self.current_thread
        })
    
    async def run(self):
        """Основной цикл чата"""
        self.clear_screen()
        self.print_header()
        self.print_status_bar()
        
        self.console.print("[dim]Type /help for available commands, /quit to exit[/dim]")
        self.console.print(Rule(style="dim"))
        
        while True:
            user_input = self.get_user_input()
            
            if user_input is None:
                break
            
            if not user_input:
                continue
            
            # Добавить в историю
            self.add_to_history(user_input, "user")
            
            # Обработка системных команд
            if user_input.startswith('/'):
                if self.process_system_command(user_input):
                    continue
            
            # Отправка агенту
            if self.agent:
                try:
                    with Status("[dim]🤔 Gemini is thinking...[/dim]", console=self.console):
                        start_time = time.time()
                        response = await self.agent.process_message(user_input, self.current_thread)
                        response_time = time.time() - start_time
                    
                    self.add_to_history(response, "agent")
                    self.display_agent_response(response, response_time)
                    
                except Exception as e:
                    error_msg = f"Error processing message: {str(e)}"
                    self.add_to_history(error_msg, "error")
                    self.display_error(error_msg)
            else:
                self.display_error("Agent not initialized")
            
            self.console.print()  # Добавить пустую строку для разделения
        
        self.console.print("[dim]Goodbye! 👋[/dim]")


# ===== ГЛАВНАЯ ФУНКЦИЯ =====
async def main():
    """Главная функция с Rich интерфейсом"""
    load_dotenv()
    
    try:
        # Создание конфигурации (только для Gemini)
        config = AgentConfig(
            filesystem_path=os.getenv("FILESYSTEM_PATH"),  # Может быть None
            model_name=os.getenv("GEMINI_MODEL", "gemini-2.5-flash"),
            temperature=float(os.getenv("TEMPERATURE", "0.0"))
        )
        
        # Создание и инициализация агента
        agent = FileSystemAgent(config)
        
        # Создаем богатый интерфейс для показа прогресса инициализации
        console = Console()
        
        with console.status("[bold green]Initializing Gemini Agent...", spinner="dots"):
            if not await agent.initialize():
                console.print("❌ [bold red]Не удалось инициализировать агента[/bold red]")
                return
        
        console.print("✅ [bold green]Gemini Agent successfully initialized![/bold green]")
        
        # Запуск богатого чата
        chat = RichInteractiveChat(agent)
        await chat.run()
        
    except Exception as e:
        Console().print(f"❌ [bold red]Критическая ошибка: {e}[/bold red]")
    
    logger.info("🏁 Завершение работы")


if __name__ == "__main__":
    asyncio.run(main())