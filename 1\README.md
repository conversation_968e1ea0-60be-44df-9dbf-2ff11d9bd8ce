# 🤖 AI FileSystem Agent

Инте<PERSON><PERSON><PERSON>ктуальный агент для работы с файловой системой с красивым терминальным интерфейсом.

## ✨ Особенности

- 🎨 **Богатый терминальный интерфейс** с использованием Rich
- 🔧 **12 мощных MCP инструментов** для работы с файловой системой
- 📁 **Умная работа с файлами** - создание, чтение, редактирование, поиск
- 🧠 **Память диалогов** - агент помнит контекст разговора
- 🤖 **Множество AI моделей** - <PERSON><PERSON><PERSON>, OpenAI, DeepSeek, Gemini
- 📊 **Визуализация файлового дерева** с эмодзи и цветами
- 📝 **История команд** с экспортом в Markdown
- ⚙️ **Система команд** для управления интерфейсом
- 🔍 **Продвинутый поиск** файлов по паттернам
- 📂 **Пакетные операции** с множественными файлами

## 🚀 Установка

1. **Клонируйте репозиторий или скопируйте файлы**

2. **Установите зависимости:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Настройте переменные окружения:**
   ```bash
   cp .env.example .env
   # Отредактируйте .env файл, добавив ваши API ключи
   ```

4. **Для использования с Ollama** (опционально):
   ```bash
   # Установите Ollama и скачайте модель
   ollama pull qwen2.5:32b
   ```

## ⚙️ Конфигурация

### Переменные окружения (.env файл):

```bash
# Выберите провайдер модели
MODEL_PROVIDER=gemini  # ollama, openai, deepseek, openrouter, gemini

# API ключи (для выбранного провайдера)
GOOGLE_API_KEY=your_key_here
OPENAI_API_KEY=your_key_here
DEEPSEEK_API_KEY=your_key_here
OPENROUTER_API_KEY=your_key_here

# Рабочая директория (опционально)
FILESYSTEM_PATH=/path/to/workspace
```

### Поддерживаемые провайдеры:

| Провайдер | Модель по умолчанию | Требует API ключ |
|-----------|-------------------|------------------|
| **Ollama** | qwen2.5:32b | ❌ |
| **OpenAI** | gpt-4o-mini | ✅ |
| **DeepSeek** | deepseek-chat | ✅ |
| **Gemini** | gemini-1.5-flash-latest | ✅ |
| **OpenRouter** | kimi-k2:free | ✅ |

## 🎮 Использование

### Запуск агента:
```bash
python 2.py
```

### Основные команды:

#### 📁 Файловые операции:
```
создай файл test.txt с текстом "Привет мир"
создай папку documents/projects
читай файл README.md
читай файлы config.json и package.json одновременно
редактируй файл app.py - замени строку 15 на новый код
переименуй файл old.txt в new.txt
найди все файлы .py в проекте
покажи дерево файлов
покажи информацию о файле data.csv
покажи файлы с размерами
```

#### 🔧 Системные команды:

| Команда | Описание |
|---------|----------|
| `/help` | Показать справку и доступные MCP инструменты |
| `/tools` | Показать все доступные MCP инструменты |
| `/clear` | Очистить экран |
| `/status` | Показать статус системы |
| `/tree [путь]` | Показать дерево файлов |
| `/history [лимит]` | История команд |
| `/thread <имя>` | Переключить контекст |
| `/export` | Экспорт истории в MD |
| `/quit` | Выйти |

#### ⚙️ Настройки:

| Команда | Описание |
|---------|----------|
| `/theme light\|dark` | Сменить тему |
| `/timestamps on\|off` | Временные метки |

## 🎨 Особенности интерфейса

### 📊 Дерево файлов
- 🐍 **Python файлы** - желтый цвет
- 🎨 **CSS** - пурпурный цвет  
- 🌐 **HTML** - синий цвет
- 📋 **JSON/YAML** - голубой цвет
- 📝 **Markdown** - белый цвет
- 🖼️ **Изображения** - пурпурный цвет

### 📈 Статус-бар показывает:
- 🔧 Активную модель AI
- 📁 Рабочую директорию
- 🧠 Статус памяти
- 🔧 Доступные MCP инструменты (12 шт.)
- 💬 Текущий thread/контекст

### ⏱️ Метрики производительности:
- Время ответа агента
- Индикатор "думает" во время обработки
- История всех команд с временными метками

## 🔧 Доступные MCP инструменты

Агент имеет доступ к 12 мощным инструментам для работы с файловой системой:

### 📁 Основные файловые операции:

| Инструмент | Описание |
|------------|----------|
| **read_file** | Чтение полного содержимого файла из файловой системы |
| **read_multiple_files** | Одновременное чтение нескольких файлов (более эффективно) |
| **write_file** | Создание нового файла или полная перезапись существующего |
| **edit_file** | Построчное редактирование файла с точными заменами |

### 📂 Управление директориями:

| Инструмент | Описание |
|------------|----------|
| **create_directory** | Создание новой директории или вложенных директорий |
| **list_directory** | Получение детального списка файлов и директорий |
| **list_directory_with_sizes** | Список файлов и директорий с указанием размеров |
| **directory_tree** | Рекурсивное дерево файлов и директорий в JSON формате |

### 🔍 Поиск и навигация:

| Инструмент | Описание |
|------------|----------|
| **move_file** | Перемещение или переименование файлов и директорий |
| **search_files** | Рекурсивный поиск файлов и директорий по паттерну |
| **get_file_info** | Получение метаданных о файле или директории |
| **list_allowed_directories** | Список директорий, к которым разрешен доступ |

### 💡 Примеры использования:

```bash
# Чтение файла
"прочитай файл config.json"

# Создание файла с содержимым
"создай файл test.py с кодом для hello world"

# Поиск файлов
"найди все python файлы в проекте"

# Создание структуры директорий
"создай папки src/components и src/utils"

# Получение информации о файле
"покажи информацию о файле README.md"
```

## 🛠️ Расширение функционала

Код спроектирован для легкого расширения:

### Добавление новых команд:
```python
def process_system_command(self, command: str) -> bool:
    if command.startswith('/mycmd'):
        # Ваша логика
        return True
    return False
```

### Добавление новых провайдеров моделей:
```python
# В AgentConfig.model_configs добавить:
"new_provider": {
    "model_name": "model-name",
    "api_key_env": "NEW_PROVIDER_API_KEY",
    "temperature": 0.0
}
```

## 🐛 Устранение неполадок

### Проблема: Предупреждения о JSON Schema
**Решение:** В коде уже настроена фильтрация этих предупреждений

### Проблема: "Агент не готов"
**Решение:** Проверьте:
- API ключи в .env файле
- Доступность выбранного провайдера
- Логи в файле `ai_agent.log`

### Проблема: Ollama не работает
**Решение:** 
```bash
# Убедитесь что Ollama запущен
ollama serve

# Проверьте доступность модели
ollama list
```

## 📁 Структура файлов

```
project/
├── 2.py              # Основной файл приложения
├── requirements.txt   # Зависимости Python  
├── .env.example      # Пример переменных окружения
├── .env              # Ваши настройки (создать)
├── ai_agent.log      # Логи приложения (создается автоматически)
└── chat_history_*.md # Экспорт истории (создается по команде)
```

## 🤝 Возможности для улучшения

### ✅ Уже реализовано:
- [x] **MCP инструменты** - 12 мощных инструментов для файловой системы
- [x] **Пакетные операции** - одновременная работа с множественными файлами
- [x] **Продвинутый поиск** - рекурсивный поиск по паттернам
- [x] **Метаданные файлов** - получение детальной информации о файлах

### 🔮 Планы на будущее:
- [ ] Web интерфейс (FastAPI + Gradio)
- [ ] Плагины для Git операций
- [ ] Интеграция с базами данных
- [ ] Система уведомлений
- [ ] Автоматическое резервное копирование
- [ ] Анализ качества кода
- [ ] Шаблоны для генерации кода
- [ ] Интеграция с облачными хранилищами

## 📄 Лицензия

MIT License - используйте свободно для любых целей.

---

**Приятного использования! 🚀**