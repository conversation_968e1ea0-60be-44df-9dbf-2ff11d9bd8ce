# Настройки модели (выберите один из провайдеров)
MODEL_PROVIDER=gemini
# MODEL_PROVIDER=ollama
# MODEL_PROVIDER=openai
# MODEL_PROVIDER=deepseek
# MODEL_PROVIDER=openrouter

# API ключи (заполните для выбранного провайдера)
GOOGLE_API_KEY=AIzaSyA57GOnA5U5DazJZwAxiVwGhCtEAJSOsPk
#OPENAI_API_KEY=your_openai_api_key_here
#DEEPSEEK_API_KEY=your_deepseek_api_key_here
#OPENROUTER_API_KEY=your_openrouter_api_key_here

# Рабочая директория (опционально, по умолчанию - текущая папка)
FILESYSTEM_PATH=d:\py_projects\ai-agent\LangGraphExample\1\

# Настройки логирования (опционально)
LOG_LEVEL=INFO