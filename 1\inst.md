Описание инструментов AI-ассистента:

*   **read_file(path, head=None, tail=None)**: Читает полное содержимое файла. Можно указать `head` для чтения первых N строк или `tail` для чтения последних N строк.
*   **read_multiple_files(paths)**: Читает содержимое нескольких файлов одновременно.
*   **write_file(content, path)**: Создает новый файл или полностью перезаписывает существующий файл новым содержимым.
*   **edit_file(edits, path, dryRun=None)**: Вносит построчные изменения в текстовый файл. Каждое изменение заменяет точные последовательности строк новым содержимым.
*   **create_directory(path)**: Создает новую директорию или гарантирует ее существование.
*   **list_directory(path)**: Получает подробный список всех файлов и директорий в указанном пути.
*   **list_directory_with_sizes(path, sortBy=None)**: Получает подробный список всех файлов и директорий в указанном пути, включая размеры.
*   **directory_tree(path)**: Получает рекурсивное древовидное представление файлов и директорий в виде JSON-структуры.
*   **move_file(destination, source)**: Перемещает или переименовывает файлы и директории.
*   **search_files(path, pattern, excludePatterns=None)**: Рекурсивно ищет файлы и директории, соответствующие шаблону.
*   **get_file_info(path)**: Извлекает подробные метаданные о файле или директории.
*   **list_allowed_directories()**: Возвращает список директорий, к которым серверу разрешен доступ.